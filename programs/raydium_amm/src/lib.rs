use anchor_lang::prelude::*;

pub mod processor;
pub mod instructions;
pub mod test;

pub use processor::*;
pub use instructions::*;

declare_id!("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8");

#[derive(Accounts)]
pub struct Initialize {}

#[derive(Accounts)]
pub struct Swap<'info> {
    #[account(mut)]
    pub amm: AccountInfo<'info>,
    pub amm_authority: AccountInfo<'info>,
    #[account(mut)]
    pub amm_open_orders: AccountInfo<'info>,
    pub amm_target_orders: AccountInfo<'info>,
    #[account(mut)]
    pub pool_coin_token_account: AccountInfo<'info>,
    #[account(mut)]
    pub pool_pc_token_account: AccountInfo<'info>,
    pub serum_program: AccountInfo<'info>,
    #[account(mut)]
    pub serum_market: AccountInfo<'info>,
    #[account(mut)]
    pub serum_bids: AccountInfo<'info>,
    #[account(mut)]
    pub serum_asks: AccountInfo<'info>,
    #[account(mut)]
    pub serum_event_queue: AccountInfo<'info>,
    #[account(mut)]
    pub serum_coin_vault_account: AccountInfo<'info>,
    #[account(mut)]
    pub serum_pc_vault_account: AccountInfo<'info>,
    pub serum_vault_signer: AccountInfo<'info>,
    #[account(mut)]
    pub user_source_token_account: AccountInfo<'info>,
    #[account(mut)]
    pub user_destination_token_account: AccountInfo<'info>,
    pub user_source_owner: Signer<'info>,
}

#[program]
pub mod raydium_amm {
    use super::*;

    pub fn initialize(_ctx: Context<Initialize>) -> Result<()> {
        Ok(())
    }

    pub fn swap(_ctx: Context<Swap>, _amount_in: u64, _minimum_amount_out: u64) -> Result<()> {
        Ok(())
    }
}
