#[cfg(test)]
mod tests {
    use crate::{processor, instructions};
    use solana_program::pubkey::Pubkey;

    #[test]
    fn test_authority_id_generation() {
        let program_id = Pubkey::new_unique();
        let nonce = 255u8;

        let authority = processor::Processor::authority_id(
            &program_id,
            processor::AUTHORITY_AMM,
            nonce,
        );

        assert!(authority.is_ok());
        println!("Generated authority: {:?}", authority.unwrap());
    }

    #[test]
    fn test_swap_instruction_creation() {
        let program_id = Pubkey::new_unique();
        let amm_id = Pubkey::new_unique();
        let amm_authority = Pubkey::new_unique();
        let amm_open_orders = Pubkey::new_unique();
        let amm_target_orders = Pubkey::new_unique();
        let pool_coin_token_account = Pubkey::new_unique();
        let pool_pc_token_account = Pubkey::new_unique();
        let serum_program_id = Pubkey::new_unique();
        let serum_market = Pubkey::new_unique();
        let serum_bids = Pubkey::new_unique();
        let serum_asks = Pubkey::new_unique();
        let serum_event_queue = Pubkey::new_unique();
        let serum_coin_vault_account = Pubkey::new_unique();
        let serum_pc_vault_account = Pubkey::new_unique();
        let serum_vault_signer = Pubkey::new_unique();
        let user_source_token_account = Pubkey::new_unique();
        let user_destination_token_account = Pubkey::new_unique();
        let user_source_owner = Pubkey::new_unique();

        let instruction = instructions::swap_base_in(
            &program_id,
            &amm_id,
            &amm_authority,
            &amm_open_orders,
            &amm_target_orders,
            &pool_coin_token_account,
            &pool_pc_token_account,
            &serum_program_id,
            &serum_market,
            &serum_bids,
            &serum_asks,
            &serum_event_queue,
            &serum_coin_vault_account,
            &serum_pc_vault_account,
            &serum_vault_signer,
            &user_source_token_account,
            &user_destination_token_account,
            &user_source_owner,
            1000000u64, // amount_in
            900000u64,  // minimum_amount_out
        );

        assert!(instruction.is_ok());
        let ix = instruction.unwrap();
        assert_eq!(ix.program_id, program_id);
        assert_eq!(ix.accounts.len(), 18); // 17 accounts + token program
        println!("Instruction created successfully with {} accounts", ix.accounts.len());
    }
}
