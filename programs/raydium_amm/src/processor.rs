use anchor_lang::prelude::*;
use solana_program::pubkey::Pubkey;

pub const AUTHORITY_AMM: &[u8] = b"amm authority";

pub struct Processor;

impl Processor {
    /// Generate AMM authority address
    pub fn authority_id(
        program_id: &Pubkey,
        authority_seed: &[u8],
        nonce: u8,
    ) -> Result<Pubkey> {
        let seeds = &[authority_seed, &[nonce]];
        let (authority, _bump) = Pubkey::find_program_address(seeds, program_id);
        Ok(authority)
    }
}
