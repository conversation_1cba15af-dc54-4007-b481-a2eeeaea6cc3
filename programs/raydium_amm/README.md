# Raydium AMM - 最小实现

这是一个为 MEV Bot 项目创建的最小 Raydium AMM Anchor 程序实现。

## 功能

该程序提供了以下核心功能，满足你的 MEV Bot 项目需求：

### 1. 处理器 (Processor)
- `Processor::authority_id()` - 生成 AMM 权限地址
- `AUTHORITY_AMM` 常量 - AMM 权限种子

### 2. 指令 (Instructions)
- `swap_base_in()` - 创建基础代币输入的交换指令

### 3. 账户结构
- `Initialize` - 初始化账户结构
- `Swap` - 交换操作账户结构

## 使用方式

在你的代码中，你可以这样使用：

```rust
use raydium_amm::instructions::swap_base_in;
use raydium_amm::processor::{Processor, AUTHORITY_AMM};

// 生成权限地址
let authority = Processor::authority_id(
    &program_id,
    AUTHORITY_AMM,
    nonce,
)?;

// 创建交换指令
let swap_instruction = swap_base_in(
    &program_id,
    &amm_id,
    &authority,
    // ... 其他参数
)?;
```

## 项目结构

```
programs/raydium_amm/
├── Cargo.toml          # 依赖配置
├── src/
│   ├── lib.rs          # 主程序入口
│   ├── processor.rs    # 处理器逻辑
│   ├── instructions.rs # 指令构建
│   └── test.rs         # 单元测试
└── README.md           # 本文档
```

## 测试

运行测试以验证功能：

```bash
cargo test -p raydium_amm
```

所有测试都应该通过，包括：
- 权限地址生成测试
- 交换指令创建测试

## 注意事项

- 这是一个最小实现，仅包含 MEV Bot 项目所需的核心功能
- 程序 ID 设置为 Raydium 主网地址：`675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8`
- 所有警告都是正常的，不影响功能使用

## 兼容性

- Anchor 版本：0.30.0
- Solana 版本：1.18.x
- 与你现有的 MEV Bot 代码完全兼容
